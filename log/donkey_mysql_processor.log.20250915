MySQLDBManager Mon, 15 Sep 2025 14:19:26 ERROR    mysql_engine.py (76)	####	(1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in ('2357936500')' at line 2")
select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and and compCode in ('2357936500')
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in ('2357936500')' at line 2")
MySQLDBManager Mon, 15 Sep 2025 14:20:00 ERROR    mysql_engine.py (76)	####	(1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500)' at line 2")
select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and and compCode in (2357936500)
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500)' at line 2")
MySQLDBManager Mon, 15 Sep 2025 14:20:31 ERROR    mysql_engine.py (76)	####	(1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500','2349740622)' at line 2")
select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and and compCode in (2357936500','2349740622)
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500','2349740622)' at line 2")
MySQLDBManager Mon, 15 Sep 2025 14:22:52 ERROR    mysql_engine.py (76)	####	(1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500','2349740622)' at line 2")
select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and and compCode in (2357936500','2349740622)
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500','2349740622)' at line 2")
MySQLDBManager Mon, 15 Sep 2025 14:23:17 ERROR    mysql_engine.py (76)	####	(1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500','2349740622)' at line 2")
select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and and compCode in (2357936500','2349740622)
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and compCode in (2357936500','2349740622)' at line 2")
MySQLDBManager Mon, 15 Sep 2025 14:51:31 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'endDate' in 'window order by'")

        select * from (
        select *,row_number() over (partition by compCode order by endDate desc) as rn2
            from (
                select totCurrAsset,totalCurrLiab,accUdepr,compCode,
                       row_number() over (partition by compCode, endDate order by reportType desc) as rn
                from sy_cd_ms_fin_sk_balance_sheet
                where  endDate like "%12-31"  and compCode in ('2357936500','2349740622') and dataStatus!=3 
            ) t
            where rn = 1 
            ) t2 where rn2=1
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'endDate' in 'window order by'")
MySQLDBManager Mon, 15 Sep 2025 14:56:03 ERROR    mysql_engine.py (76)	####	(1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'full outer join (\n            select * from (\n                select *,row_numbe' at line 25")

        select
            coalesce(lrb.compCode, fzb.compCode) as compCode,
            coalesce(lrb.endDate, fzb.endDate) as endDate,
            lrb.biztotInco,
            lrb.netPareCompProf,
            lrb.deveExpe,
            fzb.totCurrAsset,
            fzb.totalCurrLiab,
            fzb.accUdepr
        from (
            select *
            from (
                select biztotInco, netPareCompProf, deveExpe, compCode, endDate,
                       row_number() over (partition by compCode order by endDate desc) as rn2
                from (
                    select biztotInco, netPareCompProf, deveExpe, compCode, endDate,
                           row_number() over (partition by compCode, endDate order by reportType desc) as rn
                    from sy_cd_ms_fin_sk_inc_statement
                    where endDate like "%12-31" and compCode in ('2357936500','2349740622') and dataStatus != 3
                ) t
                where rn = 1
            ) t2
            where rn2 = 1
        ) lrb
        full outer join (
            select * from (
                select *,row_number() over (partition by compCode order by endDate desc) as rn2
                from (
                    select totCurrAsset,totalCurrLiab,accUdepr,compCode,endDate,
                           row_number() over (partition by compCode, endDate order by reportType desc) as rn
                    from sy_cd_ms_fin_sk_balance_sheet
                    where  endDate like "%12-31"  and compCode in ('2357936500','2349740622') and dataStatus!=3
                ) t
                where rn = 1
            ) t2 where rn2=1
        ) fzb on lrb.compCode = fzb.compCode and lrb.endDate = fzb.endDate
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'full outer join (\n            select * from (\n                select *,row_numbe' at line 25")
MySQLDBManager Mon, 15 Sep 2025 16:03:54 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
SELECT compCode, compName from sy_cd_ms_base_micro_list
                WHERE dataStatus!=3 and compCode in ('10499766','80486043','*********','*********','15204712');
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
MySQLDBManager Mon, 15 Sep 2025 16:08:06 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
SELECT compCode, compName from sy_cd_ms_base_micro_list 
                WHERE dataStatus!=3 and compCode in ('3456037210','4947320387','80486043','*********','3379742880');
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
MySQLDBManager Mon, 15 Sep 2025 16:08:35 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
SELECT compCode, compName from sy_cd_ms_base_micro_list 
                WHERE dataStatus!=3 and compCode in ('3456037210','4947320387','80486043','*********','3379742880');
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
MySQLDBManager Mon, 15 Sep 2025 16:10:48 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
SELECT compCode, compName from sy_cd_ms_base_micro_list 
                WHERE dataStatus!=3 and compCode in ('3456037210','4947320387','80486043','*********','3379742880');
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'106.120.222.78' for table 'sy_cd_ms_base_micro_list'")
MySQLDBManager Mon, 15 Sep 2025 16:58:12 ERROR    mysql_engine.py (76)	####	(2003, "Can't connect to MySQL server on 'pc-2zeh2t7shq01mm83z.rwlb.rds.aliyuncs.com' (timed out)")
SELECT compCode, compName from sy_cd_ms_base_normal_comp_list
                WHERE dataStatus!=3 and compName in ('株洲千金药业股份有限公司','浙江正裕工业股份有限公司','石家庄科林电气股份有限公司','中科星图股份有限公司','苏州天华新能源科技股份有限公司');
Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 325, in connection
    con = self._idle_cache.pop(0)
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 644, in connect
    sock = socket.create_connection(
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py", line 843, in create_connection
    raise err
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py", line 831, in create_connection
    sock.connect(sa)
socket.timeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 68, in query_data
    mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 42, in create_mysql_connection
    conn_pool = PooledDB(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 267, in __init__
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 267, in <listcomp>
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 338, in dedicated_connection
    return self.connection(False)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 327, in connection
    con = self.steady_connection()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 273, in steady_connection
    return connect(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 137, in connect
    return SteadyDBConnection(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 192, in __init__
    self._store(self._create())
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 211, in _create
    con = self._creator(*self._args, **self._kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 358, in __init__
    self.connect()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 711, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on 'pc-2zeh2t7shq01mm83z.rwlb.rds.aliyuncs.com' (timed out)")
MySQLDBManager Mon, 15 Sep 2025 16:58:22 ERROR    mysql_engine.py (76)	####	(2003, "Can't connect to MySQL server on 'pc-2zeh2t7shq01mm83z.rwlb.rds.aliyuncs.com' (timed out)")
select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and compCode in ('')
Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 325, in connection
    con = self._idle_cache.pop(0)
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 644, in connect
    sock = socket.create_connection(
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py", line 843, in create_connection
    raise err
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py", line 831, in create_connection
    sock.connect(sa)
socket.timeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 68, in query_data
    mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 42, in create_mysql_connection
    conn_pool = PooledDB(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 267, in __init__
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 267, in <listcomp>
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 338, in dedicated_connection
    return self.connection(False)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 327, in connection
    con = self.steady_connection()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 273, in steady_connection
    return connect(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 137, in connect
    return SteadyDBConnection(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 192, in __init__
    self._store(self._create())
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 211, in _create
    con = self._creator(*self._args, **self._kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 358, in __init__
    self.connect()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 711, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on 'pc-2zeh2t7shq01mm83z.rwlb.rds.aliyuncs.com' (timed out)")
MySQLDBManager Mon, 15 Sep 2025 16:58:32 ERROR    mysql_engine.py (76)	####	(2003, "Can't connect to MySQL server on 'pc-2zeh2t7shq01mm83z.rwlb.rds.aliyuncs.com' (timed out)")

        select * from (
            select *,
                   row_number() over (partition by compCode order by endDate desc) as rn_final
            from (
                select lrb.compCode, lrb.endDate,
                       lrb.biztotInco, lrb.netPareCompProf, lrb.deveExpe,
                       fzb.totCurrAsset, fzb.totalCurrLiab, fzb.accuDep,
                       row_number() over (partition by lrb.compCode, lrb.endDate order by fzb.reportType desc) as rn_merge
                from (
                    select biztotInco, netPareCompProf, deveExpe, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_inc_statement
                    where endDate like "%12-31" and compCode in ('') and dataStatus != 3 and listStatus=1
                ) lrb
                inner join (
                    select totCurrAsset, totalCurrLiab, accuDep, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_balance_sheet
                    where endDate like "%12-31" and compCode in ('') and dataStatus != 3 and listStatus=1
                ) fzb on lrb.compCode = fzb.compCode and lrb.endDate = fzb.endDate
            ) t
            where rn_merge = 1
        ) t2
        where rn_final = 1
        
Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 325, in connection
    con = self._idle_cache.pop(0)
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 644, in connect
    sock = socket.create_connection(
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py", line 843, in create_connection
    raise err
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py", line 831, in create_connection
    sock.connect(sa)
socket.timeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 68, in query_data
    mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 42, in create_mysql_connection
    conn_pool = PooledDB(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 267, in __init__
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 267, in <listcomp>
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 338, in dedicated_connection
    return self.connection(False)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 327, in connection
    con = self.steady_connection()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/PooledDB.py", line 273, in steady_connection
    return connect(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 137, in connect
    return SteadyDBConnection(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 192, in __init__
    self._store(self._create())
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 211, in _create
    con = self._creator(*self._args, **self._kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 358, in __init__
    self.connect()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 711, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on 'pc-2zeh2t7shq01mm83z.rwlb.rds.aliyuncs.com' (timed out)")
MySQLDBManager Mon, 15 Sep 2025 16:58:45 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'listStatus' in 'where clause'")

        select * from (
            select *,
                   row_number() over (partition by compCode order by endDate desc) as rn_final
            from (
                select lrb.compCode, lrb.endDate,
                       lrb.biztotInco, lrb.netPareCompProf, lrb.deveExpe,
                       fzb.totCurrAsset, fzb.totalCurrLiab, fzb.accuDep,
                       row_number() over (partition by lrb.compCode, lrb.endDate order by fzb.reportType desc) as rn_merge
                from (
                    select biztotInco, netPareCompProf, deveExpe, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_inc_statement
                    where endDate like "%12-31" and compCode in ('10499766','80486043','*********','*********','15204712') and dataStatus != 3 and listStatus=1
                ) lrb
                inner join (
                    select totCurrAsset, totalCurrLiab, accuDep, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_balance_sheet
                    where endDate like "%12-31" and compCode in ('10499766','80486043','*********','*********','15204712') and dataStatus != 3 and listStatus=1
                ) fzb on lrb.compCode = fzb.compCode and lrb.endDate = fzb.endDate
            ) t
            where rn_merge = 1
        ) t2
        where rn_final = 1
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'listStatus' in 'where clause'")
